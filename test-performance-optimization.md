# 性能優化測試指南

## 測試前準備

### 1. 重啟後端應用程序
```bash
# 在 backend/backend 目錄下執行
mvn spring-boot:run
```

### 2. 確認應用程序啟動成功
檢查控制台輸出，確認以下信息：
- Spring Boot應用程序啟動成功
- 數據庫連接正常
- Redis連接正常
- 性能監控服務已啟動

## 性能測試步驟

### 第一階段：基礎功能測試

#### 1. 測試性能監控API
```bash
# 獲取性能統計信息
curl http://localhost:8080/api/performance/stats

# 獲取Redis統計信息  
curl http://localhost:8080/api/performance/redis
```

#### 2. 測試商品查詢性能
```bash
# 分頁查詢商品
curl http://localhost:8080/api/products?page=0&size=20

# 按分類查詢商品
curl http://localhost:8080/api/products/category/1?page=0&size=10

# 查詢熱門商品
curl http://localhost:8080/api/products/hot?page=0&size=10

# 查詢推薦商品
curl http://localhost:8080/api/products/recommended?page=0&size=10
```

#### 3. 測試用戶相關查詢
```bash
# 需要先登入獲取token
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"how","password":"howhowhowtogo"}'

# 使用返回的token測試用戶訂單查詢
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/api/orders

# 測試用戶收藏查詢
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/api/favorites
```

### 第二階段：性能基準測試

#### 1. 數據庫查詢性能驗證
執行以下SQL查詢，觀察執行計劃：

```sql
-- 商品分類查詢（應使用 idx_category_status_price）
EXPLAIN SELECT * FROM products 
WHERE category_id = 1 AND status = 1 
ORDER BY price ASC LIMIT 10;

-- 用戶訂單查詢（應使用 idx_user_status_created）
EXPLAIN SELECT * FROM orders 
WHERE user_id = 3 AND status = 1 
ORDER BY created_at DESC LIMIT 10;
```

#### 2. 緩存性能測試
```bash
# 第一次請求（緩存未命中）
time curl http://localhost:8080/api/products?page=0&size=20

# 第二次請求（緩存命中）
time curl http://localhost:8080/api/products?page=0&size=20

# 檢查緩存命中率
curl http://localhost:8080/api/performance/stats
```

### 第三階段：壓力測試（可選）

如果有壓力測試工具，可以進行以下測試：

#### 1. 併發商品查詢測試
```bash
# 使用 ab 工具（如果已安裝）
ab -n 100 -c 10 http://localhost:8080/api/products

# 使用 curl 簡單併發測試
for i in {1..10}; do
  curl http://localhost:8080/api/products?page=0&size=20 &
done
wait
```

## 預期測試結果

### 1. 數據庫查詢優化效果
- ✅ EXPLAIN 查詢應顯示使用了新的複合索引
- ✅ 查詢掃描的行數應該大幅減少
- ✅ 查詢執行時間應該明顯縮短

### 2. 緩存性能效果
- ✅ 第一次請求後，相同請求應從緩存返回
- ✅ 緩存命中率應該逐漸提升
- ✅ 響應時間應該明顯改善

### 3. 性能監控效果
- ✅ 性能統計API應該返回詳細的監控數據
- ✅ 慢查詢檢測應該正常工作
- ✅ Redis操作統計應該正常記錄

## 測試結果記錄

### 優化前後對比
請記錄以下數據進行對比：

1. **數據庫查詢性能**
   - 查詢執行時間
   - 掃描行數
   - 索引使用情況

2. **緩存性能**
   - 緩存命中率
   - 響應時間改善
   - Redis操作統計

3. **系統資源使用**
   - CPU使用率
   - 內存使用情況
   - 數據庫連接數

## 問題排查

### 常見問題及解決方案

1. **應用程序啟動失敗**
   - 檢查數據庫連接配置
   - 確認Redis服務正在運行
   - 檢查端口是否被占用

2. **性能監控API無響應**
   - 檢查依賴是否正確添加
   - 確認Spring Boot Actuator已啟用
   - 檢查端點是否正確暴露

3. **索引未生效**
   - 確認索引已正確創建
   - 檢查查詢條件是否匹配索引
   - 使用EXPLAIN分析查詢計劃

## 下一步優化建議

根據測試結果，可以考慮以下進一步優化：

1. **如果查詢性能仍不理想**
   - 調整索引策略
   - 優化查詢語句
   - 考慮分區表

2. **如果緩存效果不佳**
   - 調整緩存過期時間
   - 優化緩存Key設計
   - 實現緩存預熱

3. **如果系統資源使用過高**
   - 調整連接池配置
   - 優化JVM參數
   - 考慮水平擴展
