-- 性能測試SQL腳本
-- 測試新添加的複合索引對查詢性能的影響

-- 1. 測試商品分類查詢（使用 idx_category_status_price 索引）
EXPLAIN SELECT * FROM products 
WHERE category_id = 1 AND status = 1 
ORDER BY price ASC 
LIMIT 10;

-- 2. 測試熱門商品查詢（使用 idx_status_hot_sold 索引）
EXPLAIN SELECT * FROM products 
WHERE status = 1 AND is_hot = 1 
ORDER BY sold_count DESC 
LIMIT 10;

-- 3. 測試推薦商品查詢（使用 idx_status_recommended_created 索引）
EXPLAIN SELECT * FROM products 
WHERE status = 1 AND is_recommended = 1 
ORDER BY created_at DESC 
LIMIT 10;

-- 4. 測試品牌篩選查詢（使用 idx_brand_status 索引）
EXPLAIN SELECT * FROM products 
WHERE brand = 'Apple' AND status = 1 
LIMIT 10;

-- 5. 測試用戶訂單查詢（使用 idx_user_status_created 索引）
EXPLAIN SELECT * FROM orders 
WHERE user_id = 3 AND status = 1 
ORDER BY created_at DESC 
LIMIT 10;

-- 6. 測試管理員訂單管理（使用 idx_status_created 索引）
EXPLAIN SELECT * FROM orders 
WHERE status = 1 
ORDER BY created_at DESC 
LIMIT 20;

-- 7. 測試用戶收藏查詢（使用 idx_user_itemtype_created 索引）
EXPLAIN SELECT * FROM favorites 
WHERE user_id = 3 AND item_type = 'PRODUCT' 
ORDER BY created_at DESC 
LIMIT 10;

-- 8. 測試收藏統計查詢（使用 idx_item_type 索引）
EXPLAIN SELECT COUNT(*) FROM favorites 
WHERE item_id = 1 AND item_type = 'PRODUCT';

-- 9. 測試關注關係查詢（使用 idx_follower_following 索引）
EXPLAIN SELECT * FROM user_follows 
WHERE follower_id = 3 AND following_id = 4;

-- 10. 測試粉絲列表查詢（使用 idx_following_created 索引）
EXPLAIN SELECT * FROM user_follows 
WHERE following_id = 3 
ORDER BY created_at DESC 
LIMIT 10;

-- 性能基準測試查詢
-- 這些查詢將用於測量實際執行時間

-- 商品查詢性能測試
SELECT 'Product Category Query Test' as test_name;
SELECT COUNT(*) as result_count FROM products 
WHERE category_id = 1 AND status = 1;

SELECT 'Hot Products Query Test' as test_name;
SELECT COUNT(*) as result_count FROM products 
WHERE status = 1 AND is_hot = 1;

SELECT 'Recommended Products Query Test' as test_name;
SELECT COUNT(*) as result_count FROM products 
WHERE status = 1 AND is_recommended = 1;

-- 訂單查詢性能測試
SELECT 'User Orders Query Test' as test_name;
SELECT COUNT(*) as result_count FROM orders 
WHERE user_id = 3;

-- 收藏查詢性能測試
SELECT 'User Favorites Query Test' as test_name;
SELECT COUNT(*) as result_count FROM favorites 
WHERE user_id = 3 AND item_type = 'PRODUCT';

-- 關注關係查詢性能測試
SELECT 'User Follows Query Test' as test_name;
SELECT COUNT(*) as result_count FROM user_follows 
WHERE follower_id = 3;

-- 顯示當前數據庫統計信息
SELECT 'Database Statistics' as info_type;
SELECT 
    table_name,
    table_rows,
    data_length,
    index_length,
    (data_length + index_length) as total_size
FROM information_schema.tables 
WHERE table_schema = 'java_springboot_redis_mail_login_test_250708'
AND table_name IN ('products', 'orders', 'favorites', 'user_follows')
ORDER BY total_size DESC;
