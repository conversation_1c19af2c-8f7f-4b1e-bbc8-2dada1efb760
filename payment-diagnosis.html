<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付診斷工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        .success {
            background: #28a745;
        }
        .success:hover {
            background: #218838;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .diagnosis-info {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending {
            background: #ffc107;
            color: #212529;
        }
        .status-paid {
            background: #28a745;
            color: white;
        }
        .status-cancelled {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>支付診斷和修復工具</h1>
        
        <div class="form-group">
            <label for="orderNumber">訂單號:</label>
            <input type="text" id="orderNumber" placeholder="請輸入訂單號，例如：ORD202507250940323996">
        </div>
        
        <div class="form-group">
            <button onclick="diagnosePayment()">診斷支付問題</button>
            <button onclick="clearResult()">清除結果</button>
        </div>
        
        <div id="diagnosisResult"></div>
        
        <div id="fixSection" style="display: none;">
            <h3>修復選項</h3>
            <div class="form-group">
                <label for="fixAction">修復操作:</label>
                <select id="fixAction">
                    <option value="markPaid">標記為已支付</option>
                    <option value="markFailed">標記為支付失敗</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="tradeNo">支付寶交易號 (可選):</label>
                <input type="text" id="tradeNo" placeholder="支付寶交易號">
            </div>
            
            <div class="form-group">
                <label for="buyerAccount">買家賬號 (可選):</label>
                <input type="text" id="buyerAccount" placeholder="買家支付寶賬號">
            </div>
            
            <div class="form-group">
                <button class="success" onclick="fixPayment()">執行修復</button>
                <button class="danger" onclick="hideFix()">取消</button>
            </div>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/payment';
        
        async function diagnosePayment() {
            const orderNumber = document.getElementById('orderNumber').value.trim();
            if (!orderNumber) {
                showResult('請輸入訂單號', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/diagnose/${orderNumber}`);
                const data = await response.json();
                
                if (data.success) {
                    displayDiagnosis(data.data);
                } else {
                    showResult(`診斷失敗: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`請求失敗: ${error.message}`, 'error');
            }
        }
        
        function displayDiagnosis(diagnosis) {
            let html = '<div class="diagnosis-info">';
            html += '<h3>診斷結果</h3>';
            
            if (diagnosis.error) {
                html += `<p style="color: red;">${diagnosis.error}</p>`;
            } else {
                // 訂單信息
                if (diagnosis.order) {
                    const order = diagnosis.order;
                    html += '<h4>訂單信息</h4>';
                    html += `<p><strong>訂單號:</strong> ${order.orderNumber}</p>`;
                    html += `<p><strong>狀態:</strong> <span class="status-badge status-${getStatusClass(order.status)}">${order.statusText}</span></p>`;
                    html += `<p><strong>金額:</strong> ¥${order.totalAmount}</p>`;
                    html += `<p><strong>創建時間:</strong> ${formatDate(order.createdAt)}</p>`;
                    if (order.paidAt) {
                        html += `<p><strong>支付時間:</strong> ${formatDate(order.paidAt)}</p>`;
                    }
                }
                
                // 支付信息
                if (diagnosis.payment && typeof diagnosis.payment === 'object') {
                    const payment = diagnosis.payment;
                    html += '<h4>支付信息</h4>';
                    html += `<p><strong>支付狀態:</strong> <span class="status-badge status-${getPaymentStatusClass(payment.paymentStatus)}">${payment.paymentStatusText}</span></p>`;
                    html += `<p><strong>支付金額:</strong> ¥${payment.paymentAmount}</p>`;
                    if (payment.thirdPartyTradeNo) {
                        html += `<p><strong>支付寶交易號:</strong> ${payment.thirdPartyTradeNo}</p>`;
                    }
                    if (payment.buyerAccount) {
                        html += `<p><strong>買家賬號:</strong> ${payment.buyerAccount}</p>`;
                    }
                    if (payment.paidAt) {
                        html += `<p><strong>支付時間:</strong> ${formatDate(payment.paidAt)}</p>`;
                    }
                    if (payment.callbackAt) {
                        html += `<p><strong>回調時間:</strong> ${formatDate(payment.callbackAt)}</p>`;
                    }
                } else if (diagnosis.payment) {
                    html += `<h4>支付信息</h4><p style="color: red;">${diagnosis.payment}</p>`;
                }
                
                // 問題和建議
                if (diagnosis.issues && diagnosis.issues.length > 0) {
                    html += '<h4>發現的問題</h4><ul>';
                    diagnosis.issues.forEach(issue => {
                        html += `<li style="color: red;">${issue}</li>`;
                    });
                    html += '</ul>';
                }
                
                if (diagnosis.suggestions && diagnosis.suggestions.length > 0) {
                    html += '<h4>建議</h4><ul>';
                    diagnosis.suggestions.forEach(suggestion => {
                        html += `<li style="color: blue;">${suggestion}</li>`;
                    });
                    html += '</ul>';
                }
                
                // 如果有問題，顯示修復選項
                if (diagnosis.issues && diagnosis.issues.length > 0) {
                    html += '<p><button class="success" onclick="showFix()">顯示修復選項</button></p>';
                }
            }
            
            html += '</div>';
            document.getElementById('diagnosisResult').innerHTML = html;
        }
        
        function getStatusClass(status) {
            switch (status) {
                case -1: return 'cancelled';
                case 0: return 'pending';
                case 1: return 'paid';
                case 2: return 'shipped';
                case 3: return 'completed';
                default: return 'pending';
            }
        }
        
        function getPaymentStatusClass(status) {
            switch (status) {
                case -1: return 'cancelled';
                case 0: return 'pending';
                case 1: return 'paid';
                case 2: return 'cancelled';
                default: return 'pending';
            }
        }
        
        function formatDate(dateStr) {
            if (!dateStr) return '無';
            return new Date(dateStr).toLocaleString('zh-TW');
        }
        
        function showFix() {
            document.getElementById('fixSection').style.display = 'block';
        }
        
        function hideFix() {
            document.getElementById('fixSection').style.display = 'none';
        }
        
        async function fixPayment() {
            const orderNumber = document.getElementById('orderNumber').value.trim();
            const action = document.getElementById('fixAction').value;
            const tradeNo = document.getElementById('tradeNo').value.trim();
            const buyerAccount = document.getElementById('buyerAccount').value.trim();
            
            if (!orderNumber) {
                showResult('請輸入訂單號', 'error');
                return;
            }
            
            const fixData = {
                action: action,
                tradeNo: tradeNo || 'MANUAL_FIX_' + Date.now(),
                buyerAccount: buyerAccount || '<EMAIL>'
            };
            
            try {
                const response = await fetch(`${API_BASE}/fix/${orderNumber}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(fixData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showResult(`修復成功: ${data.data}`, 'success');
                    hideFix();
                    // 重新診斷以顯示更新後的狀態
                    setTimeout(() => diagnosePayment(), 1000);
                } else {
                    showResult(`修復失敗: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`請求失敗: ${error.message}`, 'error');
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        function clearResult() {
            document.getElementById('result').innerHTML = '';
            document.getElementById('diagnosisResult').innerHTML = '';
            hideFix();
        }
        
        // 頁面加載時自動填入示例訂單號
        window.onload = function() {
            document.getElementById('orderNumber').value = 'ORD202507250940323996';
        };
    </script>
</body>
</html>
