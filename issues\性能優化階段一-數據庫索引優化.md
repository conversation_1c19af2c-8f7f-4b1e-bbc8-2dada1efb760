# 性能優化階段一：數據庫索引優化

## 執行時間
2025-01-25

## 優化目標
通過數據庫索引優化、連接池配置優化和性能監控系統建立，提升系統整體性能。

## 已完成的優化項目

### 1. 數據庫複合索引優化

#### Products表索引優化
- `idx_category_status_price`: (category_id, status, price) - 優化分類商品查詢和價格排序
- `idx_status_hot_sold`: (status, is_hot, sold_count DESC) - 優化熱門商品查詢
- `idx_status_recommended_created`: (status, is_recommended, created_at DESC) - 優化推薦商品查詢
- `idx_status_stock`: (status, stock) - 優化庫存查詢
- `idx_brand_status`: (brand, status) - 優化品牌篩選

#### Orders表索引優化
- `idx_user_status_created`: (user_id, status, created_at DESC) - 優化用戶訂單查詢
- `idx_status_created`: (status, created_at DESC) - 優化管理員訂單管理

#### Favorites表索引優化
- `idx_user_itemtype_created`: (user_id, item_type, created_at DESC) - 優化用戶收藏查詢
- `idx_item_type`: (item_id, item_type) - 優化收藏統計

#### User_follows表索引優化
- `idx_follower_following`: (follower_id, following_id) UNIQUE - 防止重複關注
- `idx_following_created`: (following_id, created_at DESC) - 優化粉絲列表查詢

#### Cart_items表索引優化
- `idx_cart_product`: (cart_id, product_id) UNIQUE - 防止重複添加商品

#### Order_items表索引優化
- `idx_order_created`: (order_id, created_at) - 優化訂單項目查詢

### 2. 數據庫連接池優化

#### HikariCP配置優化
- maximum-pool-size: 20 (增加最大連接數)
- minimum-idle: 5 (設置最小空閒連接數)
- idle-timeout: 300000ms (5分鐘空閒超時)
- max-lifetime: 1200000ms (20分鐘最大生命週期)
- connection-timeout: 20000ms (20秒連接超時)
- validation-timeout: 3000ms (3秒驗證超時)
- leak-detection-threshold: 60000ms (60秒洩漏檢測)

#### MySQL連接優化
- 添加批處理支持: rewriteBatchedStatements=true
- 啟用預編譯語句緩存: cachePrepStmts=true&useServerPrepStmts=true
- 設置緩存大小: prepStmtCacheSize=250&prepStmtCacheSqlLimit=2048

### 3. Hibernate性能優化

#### 批處理配置
- jdbc.batch_size: 50
- batch_versioned_data: true
- order_inserts: true
- order_updates: true

#### 緩存配置
- 啟用二級緩存: use_second_level_cache: true
- 啟用查詢緩存: use_query_cache: true
- 緩存實現: JCacheRegionFactory

#### 其他優化
- 關閉統計信息生成: generate_statistics: false
- 生產環境關閉SQL日誌: show-sql: false

### 4. Redis配置優化

#### 連接池優化
- max-active: 20 (增加最大連接數)
- max-idle: 10 (增加最大空閒連接數)
- min-idle: 2 (設置最小空閒連接數)
- max-wait: 5000ms (設置最大等待時間)
- timeout: 3000ms (增加超時時間)

### 5. 性能監控系統

#### 新增組件
- `PerformanceConfig`: 異步任務執行器配置
- `BatchOptimizer`: 批處理優化工具類
- `PerformanceMonitorService`: 性能監控服務
- `PerformanceController`: 性能監控API端點

#### 監控指標
- 數據庫查詢時間統計
- Redis操作時間統計
- 緩存命中率統計
- 慢查詢檢測和記錄
- 批處理統計信息

#### 新增依賴
- spring-boot-starter-actuator: Spring Boot監控
- micrometer-registry-prometheus: Prometheus指標導出
- cache-api & ehcache: JCache實現

## 預期效果

### 數據庫查詢優化
- 商品查詢性能提升50-80%
- 訂單查詢性能提升40-60%
- 用戶關注查詢性能提升60-70%

### 連接池優化
- 減少連接等待時間
- 提高併發處理能力
- 降低連接洩漏風險

### 監控能力
- 實時性能指標監控
- 慢查詢自動檢測
- 緩存效率分析

## 下一階段計劃

### 階段二：Redis緩存策略優化
- 實現多級緩存策略
- 優化緩存更新機制
- 添加緩存預熱功能

### 測試建議
1. 重啟應用程序以應用新配置
2. 訪問 `/api/performance/stats` 查看性能統計
3. 測試商品查詢、訂單查詢等核心功能
4. 觀察數據庫查詢時間和緩存命中率

## 問題修復記錄

### 2025-01-25 14:11 - Hibernate緩存配置問題
**問題**：應用程序啟動失敗，錯誤信息：
```
Unable to resolve name [org.hibernate.cache.jcache.JCacheRegionFactory] as strategy [org.hibernate.cache.spi.RegionFactory]
```

**原因**：配置了JCache二級緩存但缺少正確的依賴配置

**解決方案**：
1. 暫時禁用Hibernate二級緩存：
   - `use_second_level_cache: false`
   - `use_query_cache: false`
2. 註釋掉JCache相關依賴
3. 後續階段將重新實現更簡單的緩存策略

**狀態**：✅ 已修復

## 注意事項
- 新增的索引會增加寫操作的開銷，但大幅提升讀操作性能
- 連接池配置需要根據實際負載調整
- 性能監控會產生少量額外開銷，但提供重要的性能洞察
- 暫時禁用了Hibernate二級緩存，將在後續階段實現Redis緩存策略
