package com.example.service;

import com.example.dto.ApiResponse;
import com.example.entity.Cart;
import com.example.entity.CartItem;
import com.example.entity.Product;
import com.example.repository.CartRepository;
import com.example.repository.CartItemRepository;
import com.example.repository.ProductRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 購物車服務類
 * 
 * <AUTHOR>
 * @since 2025-01-23
 */
@Slf4j
@Service
@Transactional
public class CartService {
    
    @Autowired
    private CartRepository cartRepository;
    
    @Autowired
    private CartItemRepository cartItemRepository;
    
    @Autowired
    private ProductRepository productRepository;
    
    /**
     * 獲取用戶的購物車
     */
    public ApiResponse<Cart> getUserCart(Long userId) {
        try {
            Optional<Cart> cartOpt = cartRepository.findByUserIdAndStatusWithItems(userId, Cart.Status.ACTIVE);
            
            if (cartOpt.isEmpty()) {
                // 創建新購物車
                Cart newCart = new Cart(userId);
                Cart savedCart = cartRepository.save(newCart);
                return ApiResponse.success(savedCart);
            }
            
            Cart cart = cartOpt.get();

            // 同步更新購物車項目的商品信息（圖片、價格等）
            syncCartItemsWithProducts(cart);

            cart.calculateTotals();
            cartRepository.save(cart);

            return ApiResponse.success(cart);
        } catch (Exception e) {
            log.error("獲取用戶購物車失敗: userId={}", userId, e);
            return ApiResponse.error("獲取購物車失敗: " + e.getMessage());
        }
    }
    
    /**
     * 添加商品到購物車
     */
    public ApiResponse<String> addToCart(Long userId, Long productId, Integer quantity) {
        try {
            // 檢查商品是否存在
            Optional<Product> productOpt = productRepository.findById(productId);
            if (productOpt.isEmpty()) {
                return ApiResponse.error("商品不存在");
            }
            
            Product product = productOpt.get();
            if (product.getStatus() != Product.Status.ON_SHELF) {
                return ApiResponse.error("商品已下架");
            }
            
            if (product.getStock() < quantity) {
                return ApiResponse.error("庫存不足");
            }
            
            // 獲取或創建購物車
            Cart cart = getOrCreateCart(userId);
            
            // 檢查購物車中是否已有該商品
            Optional<CartItem> existingItemOpt = cartItemRepository.findByCartIdAndProductId(cart.getId(), productId);
            
            if (existingItemOpt.isPresent()) {
                // 更新數量
                CartItem existingItem = existingItemOpt.get();
                int newQuantity = existingItem.getQuantity() + quantity;
                
                if (product.getStock() < newQuantity) {
                    return ApiResponse.error("庫存不足");
                }
                
                existingItem.setQuantity(newQuantity);
                cartItemRepository.save(existingItem);
            } else {
                // 創建新的購物車項目
                CartItem newItem = new CartItem(
                    cart.getId(),
                    productId,
                    quantity,
                    product.getPrice(),
                    product.getName(),
                    product.getMainImageUrl()
                );
                cartItemRepository.save(newItem);
            }
            
            // 更新購物車統計
            updateCartTotals(cart.getId());
            
            return ApiResponse.success("商品已添加到購物車");
        } catch (Exception e) {
            log.error("添加商品到購物車失敗: userId={}, productId={}, quantity={}", userId, productId, quantity, e);
            return ApiResponse.error("添加到購物車失敗: " + e.getMessage());
        }
    }
    
    /**
     * 更新購物車商品數量
     */
    public ApiResponse<String> updateCartItemQuantity(Long userId, Long cartItemId, Integer quantity) {
        try {
            Optional<CartItem> itemOpt = cartItemRepository.findById(cartItemId);
            if (itemOpt.isEmpty()) {
                return ApiResponse.error("購物車項目不存在");
            }
            
            CartItem item = itemOpt.get();
            
            // 驗證購物車所有權
            Cart cart = cartRepository.findById(item.getCartId()).orElse(null);
            if (cart == null || !cart.getUserId().equals(userId)) {
                return ApiResponse.error("無權限操作此購物車");
            }
            
            // 檢查庫存
            Optional<Product> productOpt = productRepository.findById(item.getProductId());
            if (productOpt.isEmpty()) {
                return ApiResponse.error("商品不存在");
            }
            
            Product product = productOpt.get();
            if (product.getStock() < quantity) {
                return ApiResponse.error("庫存不足");
            }
            
            item.setQuantity(quantity);
            cartItemRepository.save(item);
            
            // 更新購物車統計
            updateCartTotals(cart.getId());
            
            return ApiResponse.success("購物車已更新");
        } catch (Exception e) {
            log.error("更新購物車商品數量失敗: userId={}, cartItemId={}, quantity={}", userId, cartItemId, quantity, e);
            return ApiResponse.error("更新購物車失敗: " + e.getMessage());
        }
    }
    
    /**
     * 從購物車移除商品
     */
    public ApiResponse<String> removeFromCart(Long userId, Long cartItemId) {
        try {
            Optional<CartItem> itemOpt = cartItemRepository.findById(cartItemId);
            if (itemOpt.isEmpty()) {
                return ApiResponse.error("購物車項目不存在");
            }
            
            CartItem item = itemOpt.get();
            
            // 驗證購物車所有權
            Cart cart = cartRepository.findById(item.getCartId()).orElse(null);
            if (cart == null || !cart.getUserId().equals(userId)) {
                return ApiResponse.error("無權限操作此購物車");
            }
            
            cartItemRepository.delete(item);
            
            // 更新購物車統計
            updateCartTotals(cart.getId());
            
            return ApiResponse.success("商品已從購物車移除");
        } catch (Exception e) {
            log.error("從購物車移除商品失敗: userId={}, cartItemId={}", userId, cartItemId, e);
            return ApiResponse.error("移除商品失敗: " + e.getMessage());
        }
    }
    
    /**
     * 清空購物車
     */
    public ApiResponse<String> clearCart(Long userId) {
        try {
            Optional<Cart> cartOpt = cartRepository.findByUserIdAndStatus(userId, Cart.Status.ACTIVE);
            if (cartOpt.isEmpty()) {
                return ApiResponse.success("購物車已清空");
            }
            
            Cart cart = cartOpt.get();
            cartItemRepository.deleteByCartId(cart.getId());
            
            cart.setTotalAmount(BigDecimal.ZERO);
            cart.setTotalQuantity(0);
            cartRepository.save(cart);
            
            return ApiResponse.success("購物車已清空");
        } catch (Exception e) {
            log.error("清空購物車失敗: userId={}", userId, e);
            return ApiResponse.error("清空購物車失敗: " + e.getMessage());
        }
    }
    
    /**
     * 獲取或創建購物車
     */
    private Cart getOrCreateCart(Long userId) {
        Optional<Cart> cartOpt = cartRepository.findByUserIdAndStatus(userId, Cart.Status.ACTIVE);
        if (cartOpt.isPresent()) {
            return cartOpt.get();
        }
        
        Cart newCart = new Cart(userId);
        return cartRepository.save(newCart);
    }
    
    /**
     * 切換購物車項目選中狀態
     */
    public ApiResponse<String> toggleCartItemSelected(Long userId, Long cartItemId, Integer selected) {
        try {
            Optional<CartItem> itemOpt = cartItemRepository.findById(cartItemId);
            if (itemOpt.isEmpty()) {
                return ApiResponse.error("購物車項目不存在");
            }

            CartItem item = itemOpt.get();

            // 驗證購物車所有權
            Cart cart = cartRepository.findById(item.getCartId()).orElse(null);
            if (cart == null || !cart.getUserId().equals(userId)) {
                return ApiResponse.error("無權限操作此購物車");
            }

            item.setSelected(selected);
            cartItemRepository.save(item);

            return ApiResponse.success("選中狀態已更新");
        } catch (Exception e) {
            log.error("切換購物車項目選中狀態失敗: userId={}, cartItemId={}, selected={}", userId, cartItemId, selected, e);
            return ApiResponse.error("更新選中狀態失敗: " + e.getMessage());
        }
    }

    /**
     * 獲取購物車選中項目
     */
    public ApiResponse<List<CartItem>> getSelectedCartItems(Long userId) {
        try {
            Optional<Cart> cartOpt = cartRepository.findByUserIdAndStatus(userId, Cart.Status.ACTIVE);
            if (cartOpt.isEmpty()) {
                return ApiResponse.success(List.of());
            }

            List<CartItem> selectedItems = cartItemRepository.findSelectedItemsByCartIdWithProduct(cartOpt.get().getId());
            return ApiResponse.success(selectedItems);
        } catch (Exception e) {
            log.error("獲取購物車選中項目失敗: userId={}", userId, e);
            return ApiResponse.error("獲取選中項目失敗: " + e.getMessage());
        }
    }

    /**
     * 更新購物車統計信息
     */
    private void updateCartTotals(Long cartId) {
        Optional<Cart> cartOpt = cartRepository.findById(cartId);
        if (cartOpt.isPresent()) {
            Cart cart = cartOpt.get();
            List<CartItem> items = cartItemRepository.findByCartIdOrderByCreatedAtDesc(cartId);
            cart.setCartItems(items);
            cart.calculateTotals();
            cartRepository.save(cart);
        }
    }

    /**
     * 同步購物車項目與商品的最新信息
     */
    private void syncCartItemsWithProducts(Cart cart) {
        if (cart.getCartItems() == null || cart.getCartItems().isEmpty()) {
            return;
        }

        boolean hasUpdates = false;

        for (CartItem cartItem : cart.getCartItems()) {
            Optional<Product> productOpt = productRepository.findById(cartItem.getProductId());

            if (productOpt.isPresent()) {
                Product product = productOpt.get();

                // 更新商品圖片URL（如果不一致）
                if (product.getMainImageUrl() != null &&
                    !product.getMainImageUrl().equals(cartItem.getProductImageUrl())) {
                    cartItem.setProductImageUrl(product.getMainImageUrl());
                    hasUpdates = true;
                }

                // 更新商品名稱（如果不一致）
                if (product.getName() != null &&
                    !product.getName().equals(cartItem.getProductName())) {
                    cartItem.setProductName(product.getName());
                    hasUpdates = true;
                }

                // 注意：這裡不更新價格，因為購物車中的價格是加入時的快照價格
                // 如果需要實時價格，可以在前端顯示時提示價格變化
            }
        }

        // 如果有更新，保存購物車項目
        if (hasUpdates) {
            cartItemRepository.saveAll(cart.getCartItems());
        }
    }
}
